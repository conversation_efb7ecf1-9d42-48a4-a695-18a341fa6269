/**
 * Download API Endpoints
 * REST API cho tính năng download files từ Google Drive
 */

import express from 'express';
import { FileDownloadService } from '../services/file-download-service.js';
import { SupabaseClient } from '../database/supabase.js';

const router = express.Router();
const downloadService = new FileDownloadService();
const supabase = new SupabaseClient();

/**
 * GET /api/download/users
 * L<PERSON>y danh sách users có thể download
 */
router.get('/users', async (req, res) => {
    try {
        const { data: users, error } = await supabase.getServiceClient()
            .from('scanned_users')
            .select('user_id, primary_email, full_name, given_name, family_name')
            .order('primary_email');

        if (error) {
            throw new Error(`Failed to get users: ${error.message}`);
        }

        // Thêm thống kê files cho mỗi user (chỉ files chưa download) - handle large datasets
        const usersWithStats = await Promise.all(users.map(async (user) => {
            let allFiles = [];
            let hasMore = true;
            let offset = 0;
            const batchSize = 1000; // Process in batches of 1000

            while (hasMore) {
                const { data: batchFiles, error: filesError } = await supabase.getServiceClient()
                    .from('scanned_files')
                    .select('id, size, download_status')
                    .eq('user_email', user.primary_email)
                    .or('download_status.is.null,download_status.eq.not_downloaded') // Chỉ files chưa download
                    .range(offset, offset + batchSize - 1);

                if (filesError) {
                    console.error(`Error getting files for ${user.primary_email}:`, filesError.message);
                    break; // Exit the loop on error
                }

                if (batchFiles && batchFiles.length > 0) {
                    allFiles.push(...batchFiles);

                    // Check if we got a full batch, indicating there might be more
                    hasMore = batchFiles.length === batchSize;
                    offset += batchSize;
                } else {
                    hasMore = false;
                }
            }

            const fileCount = allFiles.length;
            const totalSize = allFiles.reduce((sum, file) => sum + (parseInt(file.size) || 0), 0);

            return {
                ...user,
                fileCount,
                totalSize
            };
        }));

        res.json({
            success: true,
            data: usersWithStats
        });

    } catch (error) {
        console.error('Error getting users:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/download/users/:email/undownloaded-files
 * Lấy danh sách files chưa download của một user
 */
router.get('/users/:email/undownloaded-files', async (req, res) => {
    try {
        const { email } = req.params;

        // Fetch all undownloaded files in batches to handle large datasets
        let allFiles = [];
        let hasMore = true;
        let offset = 0;
        const batchSize = 1000; // Process in batches of 1000

        while (hasMore) {
            const { data: batchFiles, error } = await supabase.getServiceClient()
                .from('scanned_files')
                .select('id, file_id, name, size, mime_type, full_path, user_email, download_status')
                .eq('user_email', email)
                .or('download_status.is.null,download_status.eq.not_downloaded')
                .order('full_path')
                .range(offset, offset + batchSize - 1);

            if (error) {
                throw new Error(`Failed to get undownloaded files: ${error.message}`);
            }

            if (batchFiles && batchFiles.length > 0) {
                allFiles.push(...batchFiles);

                // Check if we got a full batch, indicating there might be more
                hasMore = batchFiles.length === batchSize;
                offset += batchSize;

                console.log(`📄 Fetched undownloaded files batch for ${email}: ${batchFiles.length} files (Total so far: ${allFiles.length})`);
            } else {
                hasMore = false;
            }
        }

        console.log(`✅ Total undownloaded files for ${email}: ${allFiles.length}`);

        res.json({
            success: true,
            data: allFiles
        });

    } catch (error) {
        console.error('Error getting undownloaded files:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/download/sessions
 * Tạo download session mới
 */
router.post('/sessions', async (req, res) => {
    try {
        const {
            name,
            selectedUsers,
            downloadPath,
            concurrentDownloads,
            maxRetries,
            stopOnError,
            continueOnError
        } = req.body;

        // Validate input
        if (!name || !selectedUsers || !downloadPath) {
            return res.status(400).json({
                success: false,
                error: 'Missing required fields: name, selectedUsers, downloadPath'
            });
        }

        if (!Array.isArray(selectedUsers) || selectedUsers.length === 0) {
            return res.status(400).json({
                success: false,
                error: 'selectedUsers must be a non-empty array'
            });
        }

        const session = await downloadService.createDownloadSession({
            name,
            selectedUsers,
            downloadPath,
            concurrentDownloads: concurrentDownloads || 3,
            maxRetries: maxRetries || 3,
            stopOnError: stopOnError !== undefined ? stopOnError : true,
            continueOnError: continueOnError !== undefined ? continueOnError : false
        });

        res.json({
            success: true,
            data: session
        });

    } catch (error) {
        console.error('Error creating download session:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/download/sessions
 * Lấy danh sách download sessions
 */
router.get('/sessions', async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 50;
        const sessions = await downloadService.listSessions(limit);

        res.json({
            success: true,
            data: sessions
        });

    } catch (error) {
        console.error('Error listing sessions:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/download/sessions/:id
 * Lấy thông tin chi tiết session
 */
router.get('/sessions/:id', async (req, res) => {
    try {
        const sessionId = req.params.id;
        const session = await downloadService.getSessionInfo(sessionId);

        res.json({
            success: true,
            data: session
        });

    } catch (error) {
        console.error('Error getting session info:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/download/sessions/:id/start
 * Bắt đầu download session
 */
router.post('/sessions/:id/start', async (req, res) => {
    try {
        const sessionId = req.params.id;
        const result = await downloadService.startDownloadSession(sessionId);

        res.json({
            success: true,
            data: result
        });

    } catch (error) {
        console.error('Error starting download session:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/download/sessions/:id/pause
 * Pause download session
 */
router.post('/sessions/:id/pause', async (req, res) => {
    try {
        const sessionId = req.params.id;
        const result = await downloadService.pauseDownloadSession(sessionId);

        res.json({
            success: true,
            data: result
        });

    } catch (error) {
        console.error('Error pausing download session:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/download/sessions/:id/cancel
 * Cancel download session
 */
router.post('/sessions/:id/cancel', async (req, res) => {
    try {
        const sessionId = req.params.id;
        const result = await downloadService.cancelDownloadSession(sessionId);

        res.json({
            success: true,
            data: result
        });

    } catch (error) {
        console.error('Error cancelling download session:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/download/sessions/:id/items
 * Lấy danh sách download items của session
 */
router.get('/sessions/:id/items', async (req, res) => {
    try {
        const sessionId = req.params.id;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 50;
        const status = req.query.status;

        let query = supabase.getServiceClient()
            .from('download_items')
            .select('*')
            .eq('download_session_id', sessionId)
            .order('created_at');

        if (status) {
            query = query.eq('status', status);
        }

        const offset = (page - 1) * limit;
        query = query.range(offset, offset + limit - 1);

        const { data: items, error } = await query;

        if (error) {
            throw new Error(`Failed to get download items: ${error.message}`);
        }

        // Get total count
        let countQuery = supabase.getServiceClient()
            .from('download_items')
            .select('id', { count: 'exact' })
            .eq('download_session_id', sessionId);

        if (status) {
            countQuery = countQuery.eq('status', status);
        }

        const { count, error: countError } = await countQuery;

        if (countError) {
            throw new Error(`Failed to get items count: ${countError.message}`);
        }

        res.json({
            success: true,
            data: {
                items,
                pagination: {
                    page,
                    limit,
                    total: count,
                    totalPages: Math.ceil(count / limit)
                }
            }
        });

    } catch (error) {
        console.error('Error getting download items:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/download/sessions/:id/progress
 * Lấy tiến độ realtime của session
 */
router.get('/sessions/:id/progress', async (req, res) => {
    try {
        const sessionId = req.params.id;

        // Get session info
        const session = await downloadService.getSessionInfo(sessionId);

        // Get detailed progress by status
        const { data: statusCounts, error } = await supabase.getServiceClient()
            .from('download_items')
            .select('status')
            .eq('download_session_id', sessionId);

        if (error) {
            throw new Error(`Failed to get progress: ${error.message}`);
        }

        const progress = statusCounts.reduce((acc, item) => {
            acc[item.status] = (acc[item.status] || 0) + 1;
            return acc;
        }, {});

        res.json({
            success: true,
            data: {
                session,
                progress: {
                    pending: progress.pending || 0,
                    downloading: progress.downloading || 0,
                    completed: progress.completed || 0,
                    failed: progress.failed || 0,
                    skipped: progress.skipped || 0
                }
            }
        });

    } catch (error) {
        console.error('Error getting session progress:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * DELETE /api/download/sessions/:id
 * Xóa download session
 */
router.delete('/sessions/:id', async (req, res) => {
    try {
        const sessionId = req.params.id;

        // Kiểm tra session có đang chạy không
        const session = await downloadService.getSessionInfo(sessionId);
        if (session.status === 'running') {
            return res.status(400).json({
                success: false,
                error: 'Cannot delete running session. Please cancel it first.'
            });
        }

        // Xóa session (cascade sẽ xóa download_items)
        const { error } = await supabase.getServiceClient()
            .from('download_sessions')
            .delete()
            .eq('id', sessionId);

        if (error) {
            throw new Error(`Failed to delete session: ${error.message}`);
        }

        res.json({
            success: true,
            message: 'Session deleted successfully'
        });

    } catch (error) {
        console.error('Error deleting session:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

export default router;
