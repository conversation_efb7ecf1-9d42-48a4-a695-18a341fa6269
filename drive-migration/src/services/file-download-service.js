/**
 * File Download Service
 * Service chính quản lý download process từ Google Drive
 */

import fs from 'fs';
import path from 'path';
import { SupabaseClient } from '../database/supabase.js';
import { googleDriveAPI } from '../api/google-drive-api.js';
import { DownloadWorker } from './download-worker.js';
import EventEmitter from 'events';

export class FileDownloadService extends EventEmitter {
    constructor() {
        super();
        this.supabase = new SupabaseClient();
        this.workers = new Map(); // sessionId -> DownloadWorker
        this.activeSessions = new Map(); // sessionId -> session data
    }

    /**
     * Tạo download session mới
     */
    async createDownloadSession(config) {
        try {
            const {
                name,
                selectedUsers,
                downloadPath,
                concurrentDownloads = 3,
                maxRetries = 3,
                stopOnError = true,
                continueOnError = false
            } = config;

            // Validate input
            if (!name || !selectedUsers || !downloadPath) {
                throw new Error('Missing required fields: name, selectedUsers, downloadPath');
            }

            if (!Array.isArray(selectedUsers) || selectedUsers.length === 0) {
                throw new Error('selectedUsers must be a non-empty array');
            }

            // Tạo session trong database
            const { data: session, error } = await this.supabase.getServiceClient()
                .from('download_sessions')
                .insert({
                    name,
                    selected_users: selectedUsers,
                    download_path: downloadPath,
                    concurrent_downloads: concurrentDownloads,
                    max_retries: maxRetries,
                    stop_on_error: stopOnError,
                    continue_on_error: continueOnError,
                    status: 'pending'
                })
                .select()
                .single();

            if (error) {
                throw new Error(`Failed to create download session: ${error.message}`);
            }

            // Tính toán tổng số files cần download
            await this.calculateSessionStats(session.id, selectedUsers);

            // Lấy lại session sau khi update stats
            const { data: updatedSession, error: updateError } = await this.supabase.getServiceClient()
                .from('download_sessions')
                .select('*')
                .eq('id', session.id)
                .single();

            if (updateError) {
                throw new Error(`Failed to get updated session: ${updateError.message}`);
            }

            console.log(`✅ Created download session: ${session.id} - ${name}`);
            return updatedSession;

        } catch (error) {
            console.error('❌ Error creating download session:', error.message);
            throw error;
        }
    }

    /**
     * Tính toán thống kê session (tổng files, size)
     */
    async calculateSessionStats(sessionId, selectedUsers) {
        try {
            // Query files từ scanned_files cho các users được chọn - fetch in batches to handle large datasets
            // Chỉ lấy files chưa download (download_status là null hoặc 'not_downloaded')
            let allFiles = [];
            let hasMore = true;
            let offset = 0;
            const batchSize = 1000; // Process in batches of 1000

            while (hasMore) {
                const { data: batchFiles, error } = await this.supabase.getServiceClient()
                    .from('scanned_files')
                    .select('id, file_id, name, size, mime_type, full_path, user_email, download_status')
                    .in('user_email', selectedUsers)
                    .or('download_status.is.null,download_status.eq.not_downloaded,download_status.eq.failed') // Include files that haven't been downloaded or failed
                    .range(offset, offset + batchSize - 1);

                if (error) {
                    throw new Error(`Failed to query files: ${error.message}`);
                }

                if (batchFiles && batchFiles.length > 0) {
                    allFiles.push(...batchFiles);

                    // Check if we got a full batch, indicating there might be more
                    hasMore = batchFiles.length === batchSize;
                    offset += batchSize;

                    console.log(`📄 Fetched session stats batch: ${batchFiles.length} files (Total so far: ${allFiles.length})`);
                } else {
                    hasMore = false;
                }
            }

            console.log(`✅ Total files for session stats: ${allFiles.length}`);
            const files = allFiles;

            const totalFiles = files.length;
            const totalSize = files.reduce((sum, file) => sum + (parseInt(file.size) || 0), 0);

            // Update session stats
            await this.supabase.getServiceClient()
                .from('download_sessions')
                .update({
                    total_files: totalFiles,
                    total_size: totalSize
                })
                .eq('id', sessionId);

            // Tạo download_items cho từng file
            const downloadItems = files.map(file => ({
                download_session_id: sessionId,
                scanned_file_id: file.id,
                user_email: file.user_email,
                file_id: file.file_id,
                file_name: file.name,
                file_path: file.full_path,
                file_size: parseInt(file.size) || 0,
                mime_type: file.mime_type,
                is_folder: file.mime_type === 'application/vnd.google-apps.folder',
                status: 'pending'
            }));

            // Insert download_items in batches to avoid Supabase limits
            if (downloadItems.length > 0) {
                const insertBatchSize = 500; // Smaller batch size for inserts
                let insertedCount = 0;

                for (let i = 0; i < downloadItems.length; i += insertBatchSize) {
                    const batch = downloadItems.slice(i, i + insertBatchSize);

                    const { error: itemsError } = await this.supabase.getServiceClient()
                        .from('download_items')
                        .insert(batch);

                    if (itemsError) {
                        throw new Error(`Failed to create download items batch ${i}-${i + batch.length}: ${itemsError.message}`);
                    }

                    insertedCount += batch.length;
                    console.log(`📝 Inserted download_items batch: ${batch.length} items (Total: ${insertedCount}/${downloadItems.length})`);
                }

                console.log(`✅ Successfully created ${insertedCount} download_items`);
            }

            console.log(`📊 Session stats: ${totalFiles} files, ${this.formatFileSize(totalSize)}`);

        } catch (error) {
            console.error('❌ Error calculating session stats:', error.message);
            throw error;
        }
    }

    /**
     * Bắt đầu download session
     */
    async startDownloadSession(sessionId) {
        try {
            // Kiểm tra session tồn tại
            const { data: session, error } = await this.supabase.getServiceClient()
                .from('download_sessions')
                .select('*')
                .eq('id', sessionId)
                .single();

            if (error || !session) {
                throw new Error(`Session not found: ${sessionId}`);
            }

            if (session.status !== 'pending' && session.status !== 'paused') {
                throw new Error(`Cannot start session with status: ${session.status}`);
            }

            // Update session status
            await this.supabase.getServiceClient()
                .from('download_sessions')
                .update({
                    status: 'running',
                    started_at: new Date().toISOString()
                })
                .eq('id', sessionId);

            // Tạo và khởi động worker
            const worker = new DownloadWorker(sessionId, session, this.supabase);
            this.workers.set(sessionId, worker);
            this.activeSessions.set(sessionId, session);

            // Listen to worker events
            worker.on('progress', (data) => {
                this.emit('progress', { sessionId, ...data });
            });

            worker.on('completed', (data) => {
                this.workers.delete(sessionId);
                this.activeSessions.delete(sessionId);
                this.emit('completed', { sessionId, ...data });
            });

            worker.on('error', (error) => {
                this.emit('error', { sessionId, error });
            });

            // Bắt đầu download
            worker.start();

            console.log(`🚀 Started download session: ${sessionId}`);
            return { success: true, sessionId };

        } catch (error) {
            console.error('❌ Error starting download session:', error.message);
            throw error;
        }
    }

    /**
     * Pause download session
     */
    async pauseDownloadSession(sessionId) {
        try {
            const worker = this.workers.get(sessionId);
            if (worker) {
                await worker.pause();
            }

            await this.supabase.getServiceClient()
                .from('download_sessions')
                .update({ status: 'paused' })
                .eq('id', sessionId);

            console.log(`⏸️ Paused download session: ${sessionId}`);
            return { success: true };

        } catch (error) {
            console.error('❌ Error pausing download session:', error.message);
            throw error;
        }
    }

    /**
     * Cancel download session
     */
    async cancelDownloadSession(sessionId) {
        try {
            const worker = this.workers.get(sessionId);
            if (worker) {
                await worker.cancel();
                this.workers.delete(sessionId);
                this.activeSessions.delete(sessionId);
            }

            await this.supabase.getServiceClient()
                .from('download_sessions')
                .update({
                    status: 'cancelled',
                    completed_at: new Date().toISOString()
                })
                .eq('id', sessionId);

            console.log(`❌ Cancelled download session: ${sessionId}`);
            return { success: true };

        } catch (error) {
            console.error('❌ Error cancelling download session:', error.message);
            throw error;
        }
    }

    /**
     * Lấy thông tin session
     */
    async getSessionInfo(sessionId) {
        try {
            const { data: session, error } = await this.supabase.getServiceClient()
                .from('download_session_stats')
                .select('*')
                .eq('id', sessionId)
                .single();

            if (error) {
                throw new Error(`Failed to get session info: ${error.message}`);
            }

            return session;

        } catch (error) {
            console.error('❌ Error getting session info:', error.message);
            throw error;
        }
    }

    /**
     * Lấy danh sách sessions
     */
    async listSessions(limit = 50) {
        try {
            const { data: sessions, error } = await this.supabase.getServiceClient()
                .from('download_session_stats')
                .select('*')
                .order('created_at', { ascending: false })
                .limit(limit);

            if (error) {
                throw new Error(`Failed to list sessions: ${error.message}`);
            }

            return sessions;

        } catch (error) {
            console.error('❌ Error listing sessions:', error.message);
            throw error;
        }
    }

    /**
     * Format file size
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Cleanup resources
     */
    async cleanup() {
        for (const [sessionId, worker] of this.workers) {
            try {
                await worker.cancel();
            } catch (error) {
                console.error(`Error cleaning up worker ${sessionId}:`, error.message);
            }
        }
        this.workers.clear();
        this.activeSessions.clear();
    }
}
